import 'package:flutter/material.dart';
/// Helper class for responsive design
class ResponsiveUtils {

  static double getScreenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }

  static double getScreenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }
  /// Determines if the current screen is a tablet based on the shortest side
  static bool isTablet(BuildContext context) {
    return MediaQuery.of(context).size.shortestSide >= 600;
  }

  /// Determines if the current screen is in landscape orientation
  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).size.width > MediaQuery.of(context).size.height;
  }

  /// Determines if the current screen is a small mobile device
  static bool isSmallScreen(BuildContext context) {
    return MediaQuery.of(context).size.width < 400;
  }

  /// Determines if the current screen is a medium-sized mobile device
  static bool isMediumScreen(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= 360 && width < 600;
  }

  /// Determines if the current screen is a large device (tablet or desktop)
  static bool isLargeScreen(BuildContext context) {
    return MediaQuery.of(context).size.width >= 600;
  }

  /// Gets the appropriate padding for cards based on screen size
  static double getCardPadding(BuildContext context) {
    return isTablet(context) ? 20.0 : 16.0;
  }

  /// Gets the appropriate chart height based on screen size and orientation
  static double getChartHeight(BuildContext context) {
    if (isTablet(context)) {
      return 350.0;
    } else if (isLandscape(context)) {
      return 280.0;
    } else {
      return 250.0;
    }
  }

  /// Gets the maximum content width for centered layouts
  static double getMaxContentWidth(BuildContext context) {
    return isTablet(context) ? 800.0 : 600.0;
  }

  /// Gets the appropriate cross-axis count for grid layouts
  static int getGridCrossAxisCount(BuildContext context) {
    if (isTablet(context)) {
      return isLandscape(context) ? 4 : 2;
    } else {
      return 2;
    }
  }

  /// Gets the appropriate child aspect ratio for grid layouts
  static double getGridChildAspectRatio(BuildContext context) {
    return isTablet(context) ? 1.8 : 1.5;
  }

  /// Gets the appropriate number of bars to display in bar charts
  static int? getMaxBars(BuildContext context, int totalBars) {
    final width = MediaQuery.of(context).size.width;
    if (width < 400) {
      return 8;
    } else if (width < 600) {
      return 12;
    } else if (totalBars > 15) {
      return 15;
    }
    return null;
  }
}


