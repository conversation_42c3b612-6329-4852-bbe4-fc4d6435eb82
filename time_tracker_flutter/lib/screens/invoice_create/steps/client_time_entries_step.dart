import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/screens/invoice_create/invoice_create_controller.dart';
import 'package:time_tracker_flutter/widgets/client_selector.dart';
import 'package:time_tracker_flutter/widgets/time_entry_selector.dart';

/// Step 1: Client and time entries selection
class ClientTimeEntriesStep extends StatelessWidget {
  final InvoiceCreateController controller;

  const ClientTimeEntriesStep({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).colorScheme.surface,
            Theme.of(context).colorScheme.surface.withValues(alpha: 0.95),
          ],
        ),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.person_rounded,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Client & Time Entries',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Select client and time entries for this invoice',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Client selection section
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.person_rounded,
                                color: Theme.of(context).colorScheme.primary,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'Select Client',
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          ClientSelector(
                            selectedClient: controller.selectedClient,
                            onClientSelected: controller.setSelectedClient,
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Time entries selection section
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.schedule_rounded,
                                color: Theme.of(context).colorScheme.primary,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'Select Time Entries',
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          AnimatedBuilder(
                            animation: controller,
                            builder: (context, child) {
                              // Calculate an average rate from all project rates for the selector
                              double averageRate = 50.0;
                              if (controller.selectedTimeEntries.isNotEmpty) {
                                double totalRate = 0.0;
                                int rateCount = 0;
                                for (final entry in controller.selectedTimeEntries) {
                                  final rate = controller.customRates[entry.projectId] ?? 50.0;
                                  totalRate += rate;
                                  rateCount++;
                                }
                                if (rateCount > 0) {
                                  averageRate = totalRate / rateCount;
                                }
                              }

                              return TimeEntrySelector(
                                initialSelectedIds: controller.selectedTimeEntries.map((e) => e.id).toList(),
                                onSelectionChanged: controller.setSelectedTimeEntries,
                                projectFilter: null,
                                currency: controller.currency,
                                locale: controller.locale,
                                defaultHourlyRate: averageRate,
                                onRateChanged: (rate) {
                                  // Update all project rates when the default rate changes
                                  for (final entry in controller.selectedTimeEntries) {
                                    controller.setCustomRate(entry.projectId, rate);
                                  }
                                },
                                currentHourlyRate: averageRate,
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
