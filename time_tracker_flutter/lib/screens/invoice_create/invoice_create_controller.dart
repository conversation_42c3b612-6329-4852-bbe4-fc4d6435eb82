import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/models/project.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/services/client_service.dart';
import 'package:time_tracker_flutter/services/invoice_service.dart';
import 'package:time_tracker_flutter/services/invoice_localization_service.dart';
import 'package:time_tracker_flutter/utils/time_utils.dart';

/// Controller for managing invoice creation state and business logic
class InvoiceCreateController extends ChangeNotifier {
  // Services
  final DatabaseService _databaseService = DatabaseService();
  final ClientService _clientService = ClientService();
  final InvoiceService _invoiceService = InvoiceService();

  // Form data
  Client? _selectedClient;
  List<TimeEntry> _selectedTimeEntries = [];
  Map<String, Project> _projects = {};
  final Map<String, double> _customRates = {};
  List<InvoiceLineItem> _additionalItems = [];

  // Invoice settings
  String _currency = 'USD';
  String _locale = 'en_US';
  DateTime _issueDate = DateTime.now();
  DateTime? _dueDate;
  double _taxRate = 0.0;
  String _notes = '';
  DateTime? _servicePeriodStart;
  DateTime? _servicePeriodEnd;
  bool _servicePeriodEnabled = false;

  // UI state
  int _currentStep = 0;
  bool _isLoading = false;
  bool _showPreview = false;

  // Controllers
  final TextEditingController _notesController = TextEditingController();
  final TextEditingController _taxRateController = TextEditingController();
  final Map<String, TextEditingController> _rateControllers = {};
  final Map<String, TextEditingController> _additionalItemControllers = {};
  final TextEditingController _currencyController = TextEditingController();
  final TextEditingController _localeController = TextEditingController();

  // Constructor parameters
  final Invoice? invoice;
  final Client? initialClient;
  final List<TimeEntry>? initialTimeEntries;
  final TickerProvider vsync;

  InvoiceCreateController({
    required this.invoice,
    required this.initialClient,
    required this.initialTimeEntries,
    required this.vsync,
  });

  // Getters
  Client? get selectedClient => _selectedClient;
  List<TimeEntry> get selectedTimeEntries => _selectedTimeEntries;
  Map<String, Project> get projects => _projects;
  Map<String, double> get customRates => _customRates;
  List<InvoiceLineItem> get additionalItems => _additionalItems;
  String get currency => _currency;
  String get locale => _locale;
  DateTime get issueDate => _issueDate;
  DateTime? get dueDate => _dueDate;
  double get taxRate => _taxRate;
  String get notes => _notes;
  DateTime? get servicePeriodStart => _servicePeriodStart;
  DateTime? get servicePeriodEnd => _servicePeriodEnd;
  bool get servicePeriodEnabled => _servicePeriodEnabled;
  int get currentStep => _currentStep;
  bool get isLoading => _isLoading;
  bool get showPreview => _showPreview;

  // Controller getters
  TextEditingController get notesController => _notesController;
  TextEditingController get taxRateController => _taxRateController;
  TextEditingController get currencyController => _currencyController;
  TextEditingController get localeController => _localeController;

  Future<void> initialize() async {
    _setLoading(true);

    try {
      // Load projects for rate configuration
      final projects = await _databaseService.getProjects();
      _projects = {for (var project in projects) project.id: project};

      // Initialize with provided data or existing invoice
      if (invoice != null) {
        _initializeFromExistingInvoice();
      } else {
        _initializeFromInitialData();
        // Load default settings for new invoices
        await _loadDefaultSettings();
      }
    } catch (e) {
      debugPrint('Error initializing invoice create controller: $e');
    } finally {
      _setLoading(false);
    }
  }

  void _initializeFromExistingInvoice() {
    final existingInvoice = invoice!;

    // Load existing invoice data
    _currency = existingInvoice.currency;
    _locale = existingInvoice.locale;
    _issueDate = existingInvoice.issueDate;
    _dueDate = existingInvoice.dueDate;
    _taxRate = existingInvoice.taxRate;
    _notes = existingInvoice.notes ?? '';
    _servicePeriodStart = existingInvoice.servicePeriodStart;
    _servicePeriodEnd = existingInvoice.servicePeriodEnd;
    _servicePeriodEnabled = existingInvoice.servicePeriodStart != null || existingInvoice.servicePeriodEnd != null;

    // Only load non-time-entry items as additional items
    _additionalItems = existingInvoice.additionalItems
        .where((item) => item.type != InvoiceLineItemType.timeEntry)
        .toList();

    _notesController.text = _notes;
    _taxRateController.text = _taxRate.toString();
    _currencyController.text = _currency;
    _localeController.text = _locale;

    // Load client
    _loadClientForInvoice(existingInvoice.clientId);

    // Load time entries
    _loadTimeEntriesForInvoice(existingInvoice.timeEntryIds);

    // Load custom rates from existing time entry line items
    _loadCustomRatesFromExistingItems(
      existingInvoice.additionalItems
          .where((item) => item.type == InvoiceLineItemType.timeEntry)
          .toList(),
    );
  }

  void _initializeFromInitialData() {
    _selectedClient = initialClient;
    _selectedTimeEntries = initialTimeEntries ?? [];
    _taxRateController.text = _taxRate.toString();
  }

  Future<void> _loadDefaultSettings() async {
    try {
      final settings = await _databaseService.getInvoiceSettings();
      _currency = settings.defaultCurrency;
      _locale = settings.defaultLocale;
      _taxRate = settings.defaultTaxRate;
      _notes = settings.defaultNotes ?? '';

      // Set default due date using settings
      _dueDate = DateTime.now().add(Duration(days: settings.defaultDueDays));

      // Update controllers
      _taxRateController.text = _taxRate.toString();
      _notesController.text = _notes;
      _currencyController.text = _currency;
      _localeController.text = _locale;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading default settings: $e');
    }
  }

  Future<void> _loadClientForInvoice(String clientId) async {
    try {
      final client = await _clientService.getClient(clientId);
      _selectedClient = client;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading client: $e');
    }
  }

  Future<void> _loadTimeEntriesForInvoice(List<String> timeEntryIds) async {
    try {
      final allTimeEntries = await _databaseService.getTimeEntries();
      final selectedEntries = allTimeEntries
          .where((entry) => timeEntryIds.contains(entry.id))
          .toList();

      _selectedTimeEntries = selectedEntries;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading time entries: $e');
    }
  }

  void _loadCustomRatesFromExistingItems(List<InvoiceLineItem> timeEntryItems) {
    // Extract rates from existing time entry line items
    for (final item in timeEntryItems) {
      // Try to match the item to a project based on description
      for (final project in _projects.values) {
        if (item.description.contains(project.name)) {
          _customRates[project.id] = item.rate;
          break;
        }
      }
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // State management methods
  void setCurrentStep(int step) {
    // When switching to step 1 or 2, synchronize rates
    if (step == 1 || step == 2) {
      // Ensure all project rates are properly initialized
      for (final entry in _selectedTimeEntries) {
        if (!_customRates.containsKey(entry.projectId)) {
          _customRates[entry.projectId] = 50.0; // Default rate
        }
      }
    }

    _currentStep = step;
    notifyListeners();
  }

  void togglePreview() {
    _showPreview = !_showPreview;
    notifyListeners();
  }

  void setSelectedClient(Client? client) {
    _selectedClient = client;
    notifyListeners();
  }

  void setSelectedTimeEntries(List<TimeEntry> entries) {
    _selectedTimeEntries = entries;
    notifyListeners();
  }

  void setCurrency(String currency) {
    _currency = currency;
    notifyListeners();
  }

  void setLocale(String locale) {
    _locale = locale;
    notifyListeners();
  }

  void setIssueDate(DateTime date) {
    _issueDate = date;
    // Ensure due date is not before issue date
    if (_dueDate != null && _dueDate!.isBefore(_issueDate)) {
      _dueDate = _issueDate.add(const Duration(days: 30));
    }
    notifyListeners();
  }

  void setDueDate(DateTime? date) {
    _dueDate = date;
    notifyListeners();
  }

  void setTaxRate(double rate) {
    _taxRate = rate;
    notifyListeners();
  }

  void setNotes(String notes) {
    _notes = notes;
    notifyListeners();
  }

  void setServicePeriodEnabled(bool enabled) {
    _servicePeriodEnabled = enabled;
    if (!enabled) {
      _servicePeriodStart = null;
      _servicePeriodEnd = null;
    }
    notifyListeners();
  }

  void setServicePeriodStart(DateTime? date) {
    _servicePeriodStart = date;
    notifyListeners();
  }

  void setServicePeriodEnd(DateTime? date) {
    _servicePeriodEnd = date;
    notifyListeners();
  }

  void setCustomRate(String projectId, double rate) {
    _customRates[projectId] = rate;
    notifyListeners();
  }

  void addAdditionalItem() {
    final newIndex = _additionalItems.length;
    _additionalItems.add(
      InvoiceLineItem(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        description: '',
        quantity: 1.0,
        rate: 0.0,
        amount: 0.0,
        type: InvoiceLineItemType.expense,
      ),
    );

    // Initialize controllers for the new item
    _getAdditionalItemController(newIndex, 'description', '');
    _getAdditionalItemController(newIndex, 'quantity', '1.0');
    _getAdditionalItemController(newIndex, 'rate', '0.0');
    notifyListeners();
  }

  void removeAdditionalItem(int index) {
    _additionalItems.removeAt(index);

    // Clean up controllers for the removed item
    final keysToRemove = <String>[];
    for (final key in _additionalItemControllers.keys) {
      if (key.startsWith('$index-')) {
        keysToRemove.add(key);
      }
    }
    for (final key in keysToRemove) {
      _additionalItemControllers[key]?.dispose();
      _additionalItemControllers.remove(key);
    }
    notifyListeners();
  }

  void updateAdditionalItem(
    int index, {
    String? description,
    double? quantity,
    double? rate,
  }) {
    if (index >= 0 && index < _additionalItems.length) {
      final item = _additionalItems[index];
      final updatedQuantity = quantity ?? item.quantity;
      final updatedRate = rate ?? item.rate;

      _additionalItems[index] = item.copyWith(
        description: description ?? item.description,
        quantity: updatedQuantity,
        rate: updatedRate,
        amount: updatedQuantity * updatedRate,
      );

      // Update controller text if needed
      if (description != null) {
        final controller = _getAdditionalItemController(index, 'description', description);
        if (controller.text != description) {
          controller.text = description;
        }
      }
      if (quantity != null) {
        final controller = _getAdditionalItemController(index, 'quantity', quantity.toString());
        if (controller.text != quantity.toString()) {
          controller.text = quantity.toString();
        }
      }
      if (rate != null) {
        final controller = _getAdditionalItemController(index, 'rate', rate.toString());
        if (controller.text != rate.toString()) {
          controller.text = rate.toString();
        }
      }
      notifyListeners();
    }
  }

  // Helper methods
  TextEditingController _getRateController(String projectId) {
    final currentRate = _customRates[projectId] ?? 50.0;
    if (!_rateControllers.containsKey(projectId)) {
      _rateControllers[projectId] = TextEditingController(text: currentRate.toString());
    } else {
      // Update the text controller's text to match the current rate
      final controller = _rateControllers[projectId]!;
      if (controller.text != currentRate.toString()) {
        controller.text = currentRate.toString();
      }
    }
    return _rateControllers[projectId]!;
  }

  TextEditingController _getAdditionalItemController(int index, String field, String initialValue) {
    final key = '$index-$field';
    if (!_additionalItemControllers.containsKey(key)) {
      _additionalItemControllers[key] = TextEditingController(text: initialValue);
    }
    return _additionalItemControllers[key]!;
  }

  TextEditingController getRateController(String projectId) => _getRateController(projectId);
  TextEditingController getAdditionalItemController(int index, String field, String initialValue) =>
      _getAdditionalItemController(index, field, initialValue);

  double calculateTimeEntryHours(TimeEntry entry) {
    if (entry.duration != null) {
      return parseDurationToHours(entry.duration!);
    } else if (entry.start != null && entry.end != null) {
      return calculateHours(entry.start!, entry.end!);
    }
    return 0.0;
  }

  double calculateSubtotal() {
    double subtotal = 0.0;

    // Add time entry amounts
    final projectGroups = <String, List<TimeEntry>>{};
    for (final entry in _selectedTimeEntries) {
      projectGroups.putIfAbsent(entry.projectId, () => []).add(entry);
    }

    for (final projectEntry in projectGroups.entries) {
      final projectId = projectEntry.key;
      final entries = projectEntry.value;
      final totalHours = entries.fold<double>(0, (sum, entry) => sum + calculateTimeEntryHours(entry));
      final rate = _customRates[projectId] ?? 50.0;
      subtotal += totalHours * rate;
    }

    // Add additional items
    for (final item in _additionalItems) {
      subtotal += item.amount;
    }

    return subtotal;
  }

  bool canProceedFromStep(int step) {
    switch (step) {
      case 0: // Client and time entries selection
        return _selectedClient != null && (_selectedTimeEntries.isNotEmpty || _additionalItems.isNotEmpty);
      case 1: // Rate configuration
        return true; // Always can proceed from rate config
      case 2: // Settings and preview
        return _isServicePeriodValid();
      default:
        return false;
    }
  }

  bool _isServicePeriodValid() {
    if (!_servicePeriodEnabled) {
      return true; // Valid if not enabled
    }
    
    // If enabled, both dates must be provided and end must be after or equal to start
    if (_servicePeriodStart == null || _servicePeriodEnd == null) {
      return false;
    }
    
    return _servicePeriodEnd!.isAfter(_servicePeriodStart!) || 
           _servicePeriodEnd!.isAtSameMomentAs(_servicePeriodStart!);
  }

  Future<void> saveInvoice() async {
    if (_selectedClient == null) {
      return;
    }

    _setLoading(true);

    try {
      Invoice updatedInvoice;

      if (invoice != null) {
        // Editing existing invoice - update it directly
        updatedInvoice = invoice!.copyWith(
          clientId: _selectedClient!.id,
          timeEntryIds: _selectedTimeEntries.map((e) => e.id).toList(),
          currency: _currency,
          locale: _locale,
          issueDate: _issueDate,
          dueDate: _dueDate,
          taxRate: _taxRate,
          notes: _notes.trim().isEmpty ? null : _notes.trim(),
          servicePeriodStart: _servicePeriodEnabled ? _servicePeriodStart : null,
          servicePeriodEnd: _servicePeriodEnabled ? _servicePeriodEnd : null,
          updatedAt: DateTime.now().toIso8601String(),
        );

        // Create all line items (time entries + additional items)
        final allLineItems = <InvoiceLineItem>[];

        // Add time entry line items
        final projectGroups = <String, List<TimeEntry>>{};
        for (final entry in _selectedTimeEntries) {
          projectGroups.putIfAbsent(entry.projectId, () => []).add(entry);
        }

        for (final projectEntry in projectGroups.entries) {
          final projectId = projectEntry.key;
          final entries = projectEntry.value;
          final project = _projects[projectId];

          if (project != null) {
            final totalHours = entries.fold<double>(
              0,
              (sum, timeEntry) => sum + calculateTimeEntryHours(timeEntry),
            );

            final rate = _customRates[projectId] ?? 50.0;

            allLineItems.add(InvoiceLineItem(
              description: '${project.name} - ${InvoiceLocalizationService.getTranslation('time_tracking', _locale)}',
              quantity: totalHours,
              rate: rate,
              type: InvoiceLineItemType.timeEntry,
            ));
          }
        }

        // Add additional items
        allLineItems.addAll(_additionalItems);

        updatedInvoice = updatedInvoice.copyWith(additionalItems: allLineItems);
      } else {
        // Creating new invoice
        final newInvoice = await _invoiceService.createInvoiceFromTimeEntries(
          timeEntries: _selectedTimeEntries,
          clientId: _selectedClient!.id,
          currency: _currency,
          locale: _locale,
          taxRate: _taxRate,
          issueDate: _issueDate,
          dueDate: _dueDate,
          notes: _notes.trim().isEmpty ? null : _notes.trim(),
          customRates: _customRates,
          servicePeriodStart: _servicePeriodEnabled ? _servicePeriodStart : null,
          servicePeriodEnd: _servicePeriodEnabled ? _servicePeriodEnd : null,
        );

        // Add additional line items
        updatedInvoice = newInvoice.copyWith(
          additionalItems: [...newInvoice.additionalItems, ..._additionalItems],
        );
      }

      // Recalculate totals with all items
      _invoiceService.calculateInvoiceTotals(updatedInvoice);

      // Save to database
      await _databaseService.saveInvoice(updatedInvoice);
    } catch (e) {
      debugPrint('Error saving invoice: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  @override
  void dispose() {
    _notesController.dispose();
    _taxRateController.dispose();
    for (final controller in _rateControllers.values) {
      controller.dispose();
    }
    for (final controller in _additionalItemControllers.values) {
      controller.dispose();
    }
    _currencyController.dispose();
    _localeController.dispose();
    super.dispose();
  }
}
