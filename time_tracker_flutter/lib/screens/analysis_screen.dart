import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/controllers/analysis_controller.dart';
import 'package:time_tracker_flutter/models/analysis_models.dart';
import 'package:time_tracker_flutter/screens/analysis/analysis_filter_widget.dart';
import 'package:time_tracker_flutter/screens/analysis/overview_tab_widget.dart';
import 'package:time_tracker_flutter/screens/analysis/projects_tab_widget.dart';
import 'package:time_tracker_flutter/screens/analysis/trends_tab_widget.dart';
import 'package:time_tracker_flutter/utils/responsive_utils.dart';
import 'package:time_tracker_flutter/widgets/analysis/analysis_ui_components.dart';
import 'package:time_tracker_flutter/widgets/analysis/records_widget.dart';
import 'package:time_tracker_flutter/utils/ui_helpers.dart';

class AnalysisScreen extends StatefulWidget {
  const AnalysisScreen({super.key});

  @override
  State<AnalysisScreen> createState() => _AnalysisScreenState();
}

class _AnalysisScreenState extends State<AnalysisScreen> with SingleTickerProviderStateMixin {
  final AnalysisController _controller = AnalysisController();
  final ScrollController _scrollController = ScrollController();

  bool _isFilterExpanded = false;
  bool _isFilterVisible = true;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _scrollController.addListener(_onScroll);
    _initialize();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.offset > 50 && _isFilterVisible) {
      setState(() {
        _isFilterVisible = false;
      });
    } else if (_scrollController.offset <= 10 && !_isFilterVisible) {
      setState(() {
        _isFilterVisible = true;
      });
    }
  }

  void _toggleFilterExpanded() {
    setState(() {
      _isFilterExpanded = !_isFilterExpanded;
    });
  }

  Future<void> _initialize() async {
    try {
      await _controller.initialize();
    } catch (e) {
      debugPrint("Error initializing analysis: $e");
      if (mounted) {
        showErrorSnackBar('Error loading analysis data: $e', context);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, _) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('Time Analysis'),
            scrolledUnderElevation: 4.0,
            elevation: 0,
            bottom: TabBar(
              controller: _tabController,
              isScrollable: true,
              dividerColor: Colors.transparent,
              indicatorSize: TabBarIndicatorSize.label,
              labelStyle: const TextStyle(fontWeight: FontWeight.bold),
              tabs: const [
                Tab(text: 'Overview'),
                Tab(text: 'Projects'),
                Tab(text: 'Trends'),
                Tab(text: 'Records'),
              ],
            ),
            actions: [
              IconButton(
                icon: Icon(_isFilterExpanded ? Icons.filter_list_off : Icons.filter_list),
                tooltip: _isFilterExpanded ? 'Hide filters' : 'Show filters',
                onPressed: _toggleFilterExpanded,
              ),
            ],
          ),
          body: _controller.isLoading
              ? Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const CircularProgressIndicator(),
                      const SizedBox(height: 16),
                      Text(
                        'Loading analysis data...',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ],
                  ),
                )
              : SafeArea(
                  child: Column(
                    children: [
                      // Animated filter bar
                      AnalysisFilterWidget(
                        projects: _controller.projects,
                        selectedProjectId: _controller.selectedProjectId,
                        selectedPeriod: _controller.selectedPeriod,
                        dateRange: _controller.dateRange,
                        isExpanded: _isFilterExpanded,
                        onProjectChanged: _controller.updateSelectedProject,
                        onPeriodChanged: _controller.updatePeriod,
                        onDateRangeChanged: _controller.updateDateRange,
                        onToggleExpanded: _toggleFilterExpanded,
                      ),

                      // Main content
                      Expanded(
                        child: NestedScrollView(
                          controller: _scrollController,
                          headerSliverBuilder: (context, innerBoxIsScrolled) {
                            return [
                              SliverToBoxAdapter(
                                child: !_isFilterVisible && !_isFilterExpanded
                                    ? AnalysisUIComponents.buildCollapsedFilterIndicator(
                                        context,
                                        _toggleFilterExpanded
                                      )
                                    : null,
                              ),
                            ];
                          },
                          body: TabBarView(
                            controller: _tabController,
                            children: [
                              // Overview Tab
                              _controller.analysisResult != null
                                  ? OverviewTabWidget(
                                      analysisResult: _controller.analysisResult!,
                                      analysisService: _controller.analysisService,
                                      selectedPeriod: _controller.selectedPeriod,
                                    )
                                  : Center(
                                      child: Text(
                                        'No data available',
                                        style: Theme.of(context).textTheme.bodyLarge,
                                      ),
                                    ),

                              // Projects Tab
                              _controller.analysisResult != null
                                  ? ProjectsTabWidget(
                                      analysisResult: _controller.analysisResult!,
                                      analysisService: _controller.analysisService,
                                      selectedPeriod: _controller.selectedPeriod,
                                    )
                                  : Center(
                                      child: Text(
                                        'No project data available',
                                        style: Theme.of(context).textTheme.bodyLarge,
                                      ),
                                    ),

                              // Trends Tab
                              _controller.analysisResult != null
                                  ? TrendsTabWidget(
                                      analysisResult: _controller.analysisResult!,
                                      analysisService: _controller.analysisService,
                                      selectedPeriod: _controller.selectedPeriod,
                                    )
                                  : Center(
                                      child: Text(
                                        'No trend data available',
                                        style: Theme.of(context).textTheme.bodyLarge,
                                      ),
                                    ),

                              // Records Tab
                              _controller.analysisResult != null
                                  ? RecordsWidget(
                                      analysisResult: _controller.analysisResult!,
                                      analysisService: _controller.analysisService,
                                    )
                                  : Center(
                                      child: Text(
                                        'No record data available',
                                        style: Theme.of(context).textTheme.bodyLarge,
                                      ),
                                    ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
          floatingActionButton: !_isFilterVisible && !_isFilterExpanded
              ? FloatingActionButton.small(
                  onPressed: _toggleFilterExpanded,
                  tooltip: 'Show filters',
                  child: const Icon(Icons.filter_list),
                )
              : null,
        );
      }
    );
  }
















}