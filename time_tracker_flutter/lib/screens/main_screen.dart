import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/screens/backup_list_screen.dart';
import 'package:time_tracker_flutter/screens/cloud_backup_list_screen.dart';
import 'package:time_tracker_flutter/screens/settings_screen.dart';
import 'package:time_tracker_flutter/screens/projects_screen.dart';
import 'package:time_tracker_flutter/screens/analysis_screen.dart';
import 'package:time_tracker_flutter/screens/invoice_list_screen.dart';
import 'package:time_tracker_flutter/services/backup_service.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/widgets/confirm_dialog.dart';
import 'package:time_tracker_flutter/widgets/cloud_backup_prompt.dart';
import 'package:share_plus/share_plus.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:time_tracker_flutter/models/backup_data.dart';
import 'package:time_tracker_flutter/widgets/reports_dialog.dart';
import 'package:time_tracker_flutter/utils/ui_helpers.dart';

// Enum to represent the actual navigation destinations
enum NavigationTab {
  home,
  analysis,
  invoices,
  settings,
}

// Enum to represent all bottom bar items (including action items)
enum BottomBarItem {
  home,
  analysis,
  invoices,
  backup,
  cloud,
  reports,
  settings,
}

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  // The current visible tab (only home or settings)
  NavigationTab _currentTab = NavigationTab.home;

  // The selected item in the bottom bar
  BottomBarItem _selectedBottomBarItem = BottomBarItem.home;

  final DatabaseService _databaseService = DatabaseService();
  final BackupService _backupService = BackupService();
  bool _isAutoBackupEnabled = false;
  bool _isCloudAutoBackupEnabled = false;
  bool _isLoading = false;
  bool _isExporting = false;
  bool _isImporting = false;
  bool _isBackingUp = false;
  bool _isCloudBackingUp = false;

  // New backup prompt state variables
  bool _newerBackupExists = false;
  String _newestBackup = '';
  bool _showBackupPrompt = false;
  bool _isRestoringFromPrompt = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
    _checkForNewerBackups();
  }

  // Add method to check for newer backups
  Future<void> _checkForNewerBackups() async {
    try {
      final backups = await _databaseService.listCloudBackups();
      if (backups.isNotEmpty) {
        final latestBackup = backups.first;

        final cloudBackupService = _databaseService.getCloudBackupService();
        final backupDate = await cloudBackupService.extractDateFromBackupFilename(latestBackup);

        if (backupDate != null) {
          final lastRestoreDate = await _databaseService.getLastRestoreDate();

          if (lastRestoreDate == null || backupDate.isAfter(lastRestoreDate)) {
            setState(() {
              _newestBackup = latestBackup;
              _newerBackupExists = true;
              _showBackupPrompt = true;
            });
          }
        }
      }
    } catch (e) {
      debugPrint("Error checking for newer backups: $e");
      // Don't show error toast here as it's not critical functionality
    }
  }

  // Add method to handle restore from prompt
  Future<void> _handleRestoreFromPrompt() async {
    setState(() {
      _isRestoringFromPrompt = true;
      _showBackupPrompt = false;
    });

    try {
      // Restore from the newest backup
      await _databaseService.restoreFromCloud(_newestBackup);

      // Save the restore date
      await _databaseService.setLastRestoreDate(DateTime.now());

      if (mounted) {
        showSuccessSnackBar('Backup restored successfully', context);
      }
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error restoring backup: ${e.toString()}', context);
      }
    } finally {
      setState(() {
        _isRestoringFromPrompt = false;
        _newerBackupExists = false;
      });
    }
  }

  // Add method to dismiss backup prompt
  void _handleDismissBackupPrompt() async {
    // Update the last restore date to current time to prevent showing this prompt again
    await _databaseService.setLastRestoreDate(DateTime.now());

    setState(() {
      _showBackupPrompt = false;
      _newerBackupExists = false;
    });
  }

  Future<void> _loadSettings() async {
    final isAutoBackupEnabled = await _databaseService.isAutoBackupEnabled();
    final isCloudAutoBackupEnabled = await _databaseService.isCloudAutoBackupEnabled();
    setState(() {
      _isAutoBackupEnabled = isAutoBackupEnabled;
      _isCloudAutoBackupEnabled = isCloudAutoBackupEnabled;
    });
  }

  void _handleBottomBarTap(int index) {
    final tappedItem = BottomBarItem.values[index];

    setState(() {
      _selectedBottomBarItem = tappedItem;
    });

    switch (tappedItem) {
      case BottomBarItem.home:
        setState(() {
          _currentTab = NavigationTab.home;
        });
        break;
      case BottomBarItem.analysis:
        setState(() {
          _currentTab = NavigationTab.analysis;
        });
        break;
      case BottomBarItem.invoices:
        setState(() {
          _currentTab = NavigationTab.invoices;
        });
        break;
      case BottomBarItem.settings:
        setState(() {
          _currentTab = NavigationTab.settings;
        });
        break;
      case BottomBarItem.backup:
        _showBackupActionSheet(context);
        break;
      case BottomBarItem.cloud:
        _showCloudActionSheet(context);
        break;
      case BottomBarItem.reports:
        _showReportsDialog();
        break;
    }
  }

  void _showReportsDialog() {
    showDialog(
      context: context,
      builder: (context) => const ReportsDialog(),
    );
  }

  void _showBackupActionSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Theme.of(context).colorScheme.surface,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) {
          return SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  height: 4,
                  width: 40,
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'Backup Options',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                ),
                ListTile(
                  leading: _isBackingUp
                      ? const SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.backup),
                  title: const Text('Create Backup'),
                  onTap: _isBackingUp
                      ? null
                      : () async {
                          // Update local state
                          setModalState(() {
                            _isBackingUp = true;
                          });

                          // Also update parent state
                          setState(() {
                            _isBackingUp = true;
                          });

                          try {
                            final filename = await _backupService.createBackup();

                            if (mounted) {
                              Navigator.pop(context);

                              showSuccessSnackBar('Backup created: $filename', context);
                            }
                          } catch (e) {
                            if (mounted) {
                              setModalState(() {
                                _isBackingUp = false;
                              });

                              setState(() {
                                _isBackingUp = false;
                              });

                              showErrorSnackBar('Error creating backup: ${e.toString()}', context);
                            }
                          }
                        },
                ),
                ListTile(
                  leading: const Icon(Icons.folder),
                  title: const Text('Manage Backups'),
                  onTap: () {
                    Navigator.pop(context);
                    _navigateToBackupList();
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.file_download),
                  title: const Text('Export Data'),
                  onTap: () {
                    Navigator.pop(context);
                    _exportData();
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.file_upload),
                  title: const Text('Import Data'),
                  onTap: () {
                    Navigator.pop(context);
                    _importData();
                  },
                ),
                SwitchListTile(
                  secondary: const Icon(Icons.auto_mode),
                  title: const Text('Auto Backup'),
                  value: _isAutoBackupEnabled,
                  onChanged: (value) {
                    Navigator.pop(context);
                    _toggleAutoBackup(value);
                  },
                ),
              ],
            ),
          );
        }
      ),
    );
  }

  void _showCloudActionSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Theme.of(context).colorScheme.surface,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) {
          return SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  height: 4,
                  width: 40,
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'Cloud Backup Options',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                ),
                ListTile(
                  leading: _isCloudBackingUp
                      ? const SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.cloud_upload),
                  title: const Text('Create Cloud Backup'),
                  onTap: _isCloudBackingUp
                      ? null
                      : () async {
                          // Update local state
                          setModalState(() {
                            _isCloudBackingUp = true;
                          });

                          // Also update parent state
                          setState(() {
                            _isCloudBackingUp = true;
                          });

                          try {
                            await _databaseService.backupToCloud();

                            if (mounted) {
                              Navigator.pop(context);
                              showSuccessSnackBar('Cloud backup created successfully', context);
                            }
                          } catch (e) {
                            if (mounted) {
                              showErrorSnackBar('Error creating cloud backup: ${e.toString()}', context);
                            }
                          } finally {
                            setModalState(() {
                              _isCloudBackingUp = false;
                            });

                            setState(() {
                              _isCloudBackingUp = false;
                            });
                          }
                        },
                ),
                ListTile(
                  leading: const Icon(Icons.cloud),
                  title: const Text('Manage Cloud Backups'),
                  onTap: () {
                    Navigator.pop(context);
                    _navigateToCloudBackupList();
                  },
                ),
                SwitchListTile(
                  secondary: const Icon(Icons.auto_mode),
                  title: const Text('Cloud Auto Backup'),
                  value: _isCloudAutoBackupEnabled,
                  onChanged: (value) {
                    Navigator.pop(context);
                    _toggleCloudAutoBackup(value);
                  },
                ),
              ],
            ),
          );
        }
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          _buildBody(),
          if (_isRestoringFromPrompt) _buildRestoringOverlay(),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _selectedBottomBarItem.index,
        onTap: _handleBottomBarTap,
        selectedItemColor: Theme.of(context).colorScheme.primary,
        unselectedItemColor: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
        type: BottomNavigationBarType.fixed,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.analytics),
            label: 'Analysis',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.receipt_long),
            label: 'Invoices',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.backup),
            label: 'Backup',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.cloud),
            label: 'Cloud',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.bar_chart),
            label: 'Reports',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'Settings',
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    switch (_currentTab) {
      case NavigationTab.home:
        return _wrapWithBackupPrompt(const ProjectsScreen());
      case NavigationTab.analysis:
        return const AnalysisScreen();
      case NavigationTab.invoices:
        return const InvoiceListScreen();
      case NavigationTab.settings:
        return const SettingsScreen();
    }
  }

  // Wrap the content with the backup prompt if needed
  Widget _wrapWithBackupPrompt(Widget content) {
    if (!_showBackupPrompt || !_newerBackupExists) {
      return content;
    }

    final backupPrompt = CloudBackupPrompt(
      onDismiss: _handleDismissBackupPrompt,
      onRestore: _handleRestoreFromPrompt,
      isRestoring: _isRestoringFromPrompt,
    );

    // For ProjectsScreen, we need to handle it differently since it has its own Scaffold
    if (content is ProjectsScreen) {
      return ProjectsScreen(
        isInTabView: true,
        backupPromptWidget: backupPrompt,
      );
    }

    // For other screens
    return Column(
      children: [
        backupPrompt,
        Expanded(child: content),
      ],
    );
  }

  // Backup & Restore methods
  Future<void> _toggleAutoBackup(bool value) async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _databaseService.setAutoBackup(value);
      setState(() {
        _isAutoBackupEnabled = value;
      });
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error toggling auto backup: ${e.toString()}', context);
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _exportData() async {
    setState(() {
      _isExporting = true;
    });

    try {
      final backupData = await _databaseService.exportDatabase();
      final jsonString = jsonEncode(backupData.toJson());

      final directory = await getTemporaryDirectory();
      final fileName = 'time-tracker-backup-${DateTime.now().toIso8601String().split('T')[0]}.json';
      final filePath = '${directory.path}/$fileName';

      final file = File(filePath);
      await file.writeAsString(jsonString);

      if (mounted) {
        await Share.shareXFiles(
          [XFile(filePath)],
          subject: 'Time Tracker Backup',
        );
      }
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error exporting data: ${e.toString()}', context);
      }
    } finally {
      setState(() {
        _isExporting = false;
      });
    }
  }

  Future<void> _importData() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => const ConfirmDialog(
        title: 'Import Data',
        content: 'This will replace all existing data. Are you sure you want to continue?',
        confirmText: 'Import',
      ),
    );

    if (confirmed != true) {
      return;
    }

    setState(() {
      _isImporting = true;
    });

    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);
        final jsonString = await file.readAsString();
        final jsonData = jsonDecode(jsonString);
        final backupData = BackupData.fromJson(jsonData);

        await _databaseService.importDatabase(backupData);

        if (mounted) {
          showSuccessSnackBar('Data imported successfully', context);
        }
      }
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error importing data: ${e.toString()}', context);
      }
    } finally {
      setState(() {
        _isImporting = false;
      });
    }
  }

  void _navigateToBackupList() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const BackupListScreen(),
      ),
    );
  }

  // Cloud backup methods
  Future<void> _toggleCloudAutoBackup(bool value) async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _databaseService.setCloudAutoBackup(value);
      setState(() {
        _isCloudAutoBackupEnabled = value;
      });
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error toggling cloud auto backup: ${e.toString()}', context);
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _navigateToCloudBackupList() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CloudBackupListScreen(),
      ),
    );
  }

  // Build the restoring overlay
  Widget _buildRestoringOverlay() {
    return Container(
      color: Colors.black54,
      alignment: Alignment.center,
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 24),
            Text(
              'Restoring Backup',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              'Please wait while your data is being restored...',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
