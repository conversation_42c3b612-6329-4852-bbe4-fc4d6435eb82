import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../services/invoice_localization_service.dart';

class ServicePeriodSelector extends StatefulWidget {
  final DateTime? initialStartDate;
  final DateTime? initialEndDate;
  final bool initialEnabled;
  final ValueChanged<DateTime?> onStartDateChanged;
  final ValueChanged<DateTime?> onEndDateChanged;
  final ValueChanged<bool> onEnabledChanged;
  final String? locale;
  final String language;

  const ServicePeriodSelector({
    super.key,
    this.initialStartDate,
    this.initialEndDate,
    this.initialEnabled = false,
    required this.onStartDateChanged,
    required this.onEndDateChanged,
    required this.onEnabledChanged,
    this.locale,
    this.language = 'en_US',
  });

  @override
  State<ServicePeriodSelector> createState() => _ServicePeriodSelectorState();
}

class _ServicePeriodSelectorState extends State<ServicePeriodSelector> {
  late bool _isEnabled;
  DateTime? _startDate;
  DateTime? _endDate;
  String? _validationError;

  @override
  void initState() {
    super.initState();
    _isEnabled = widget.initialEnabled;
    _startDate = widget.initialStartDate;
    _endDate = widget.initialEndDate;
    _validateDates();
  }

  @override
  void didUpdateWidget(ServicePeriodSelector oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.initialEnabled != widget.initialEnabled) {
      _isEnabled = widget.initialEnabled;
    }
    if (oldWidget.initialStartDate != widget.initialStartDate) {
      _startDate = widget.initialStartDate;
    }
    if (oldWidget.initialEndDate != widget.initialEndDate) {
      _endDate = widget.initialEndDate;
    }
    _validateDates();
  }

  void _validateDates() {
    if (!_isEnabled) {
      _validationError = null;
      return;
    }

    // No error if no dates are selected
    if (_startDate == null && _endDate == null) {
      _validationError = null;
      return;
    }

    // No error if only start date is selected (user is still selecting)
    if (_startDate != null && _endDate == null) {
      _validationError = null;
      return;
    }

    // No error if only end date is selected (user is still selecting)
    if (_startDate == null && _endDate != null) {
      _validationError = null;
      return;
    }

    // Only validate when both dates are selected
    if (_startDate != null && _endDate != null) {
      if (_endDate!.isBefore(_startDate!)) {
        _validationError = _getLocalizedText('endDateMustBeAfterStart');
        return;
      }
    }

    _validationError = null;
  }

  String _getLocalizedText(String key) {
    // UI should always be in English, only invoice content is localized
    switch (key) {
      case 'servicePeriod':
        return 'Service Period';
      case 'enableServicePeriod':
        return 'Enable Service Period';
      case 'from':
        return 'From';
      case 'to':
        return 'To';
      case 'bothDatesRequired':
        return 'Both start and end dates are required when service period is enabled';
      case 'endDateMustBeAfterStart':
        return 'End date must be after or equal to start date';
      case 'selectDate':
        return 'Select date';
      default:
        return key;
    }
  }

  String _formatDate(DateTime? date) {
    if (date == null) return '';
    
    try {
      final formatter = DateFormat.yMd(widget.locale);
      return formatter.format(date);
    } catch (e) {
      // Fallback to default format if locale is invalid
      return DateFormat.yMd().format(date);
    }
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate 
          ? (_startDate ?? DateTime.now())
          : (_endDate ?? _startDate ?? DateTime.now()),
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
      helpText: _getLocalizedText('selectDate'),
    );

    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
          widget.onStartDateChanged(picked);
        } else {
          _endDate = picked;
          widget.onEndDateChanged(picked);
        }
        _validateDates();
      });
    }
  }

  void _onEnabledChanged(bool? value) {
    setState(() {
      _isEnabled = value ?? false;
      if (!_isEnabled) {
        _startDate = null;
        _endDate = null;
        widget.onStartDateChanged(null);
        widget.onEndDateChanged(null);
      }
      _validateDates();
    });
    widget.onEnabledChanged(_isEnabled);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Card(
      elevation: 0,
      color: colorScheme.surfaceContainerLowest,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with checkbox
            Row(
              children: [
                Checkbox(
                  value: _isEnabled,
                  onChanged: _onEnabledChanged,
                  semanticLabel: _getLocalizedText('enableServicePeriod'),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _getLocalizedText('servicePeriod'),
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: colorScheme.onSurface,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),

            // Date selection fields (only shown when enabled)
            if (_isEnabled) ...[
              const SizedBox(height: 16),
              
              // Start date field
              _buildDateField(
                context: context,
                label: _getLocalizedText('from'),
                date: _startDate,
                onTap: () => _selectDate(context, true),
                isEnabled: _isEnabled,
              ),
              
              const SizedBox(height: 12),
              
              // End date field
              _buildDateField(
                context: context,
                label: _getLocalizedText('to'),
                date: _endDate,
                onTap: () => _selectDate(context, false),
                isEnabled: _isEnabled,
              ),

              // Validation error message
              if (_validationError != null) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: colorScheme.errorContainer,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 16,
                        color: colorScheme.onErrorContainer,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _validationError!,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: colorScheme.onErrorContainer,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDateField({
    required BuildContext context,
    required String label,
    required DateTime? date,
    required VoidCallback onTap,
    required bool isEnabled,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return InkWell(
      onTap: isEnabled ? onTap : null,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
        decoration: BoxDecoration(
          border: Border.all(
            color: isEnabled 
                ? colorScheme.outline 
                : colorScheme.outline.withOpacity(0.5),
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              Icons.calendar_today,
              size: 20,
              color: isEnabled 
                  ? colorScheme.onSurfaceVariant 
                  : colorScheme.onSurfaceVariant.withOpacity(0.5),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: isEnabled 
                          ? colorScheme.onSurfaceVariant 
                          : colorScheme.onSurfaceVariant.withOpacity(0.5),
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    date != null ? _formatDate(date) : _getLocalizedText('selectDate'),
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: date != null 
                          ? (isEnabled ? colorScheme.onSurface : colorScheme.onSurface.withOpacity(0.5))
                          : (isEnabled ? colorScheme.onSurfaceVariant : colorScheme.onSurfaceVariant.withOpacity(0.5)),
                    ),
                  ),
                ],
              ),
            ),
            if (isEnabled)
              Icon(
                Icons.arrow_drop_down,
                color: colorScheme.onSurfaceVariant,
              ),
          ],
        ),
      ),
    );
  }
}