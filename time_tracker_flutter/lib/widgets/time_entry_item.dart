import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:intl/intl.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/utils/time_utils.dart';
import 'package:time_tracker_flutter/utils/responsive_utils.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';

class TimeEntryItem extends StatefulWidget {
  final TimeEntry entry;
  final Function(TimeEntry) onEdit;
  final Function(TimeEntry) onDelete;
  final Color? projectColor;

  const TimeEntryItem({
    super.key,
    required this.entry,
    required this.onEdit,
    required this.onDelete,
    this.projectColor,
  });

  @override
  State<TimeEntryItem> createState() => _TimeEntryItemState();
}

class _TimeEntryItemState extends State<TimeEntryItem> {
  Timer? _timer;
  String _currentDuration = '';
  late Future<String> _formattedDateFuture;

  @override
  void initState() {
    super.initState();
    _formattedDateFuture = _formatDate(widget.entry.date);
    _updateDuration();
    if (widget.entry.isActive) {
      _timer = Timer.periodic(const Duration(seconds: 1), (_) => _updateDuration());
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  void didUpdateWidget(TimeEntryItem oldWidget) {
    super.didUpdateWidget(oldWidget);

    // If the entry changed, update cached data and timer if needed
    if (oldWidget.entry.id != widget.entry.id) {
      // Recompute formatted date when the entry changes
      _formattedDateFuture = _formatDate(widget.entry.date);
    }

    // Update timer based on active state changes
    if (oldWidget.entry.isActive != widget.entry.isActive) {
      _timer?.cancel();
      _timer = null;

      if (widget.entry.isActive) {
        _timer = Timer.periodic(const Duration(seconds: 1), (_) => _updateDuration());
      }
    }

    _updateDuration();
  }

  void _updateDuration() {
    setState(() {
      _currentDuration = _calculateDuration();
    });
  }

  Future<String> _formatDate(String dateStr) async {
    final date = DateTime.parse(dateStr);
    final weekday = formatWeekdayWithContext(date, context);
    String formattedDate = await LocaleDateUtils.formatDateWithCustomAndContext(date, context);

    if (mounted && ResponsiveUtils.isSmallScreen(context)) {
      formattedDate = LocaleDateUtils.shortenYearsInText(formattedDate);
    }

    return '$weekday, $formattedDate';
  }

  String _formatTimeRange(String start, String end) {
    // For active entries, just show start time with indicator
    if (widget.entry.isActive) {
      return start;
    }
    return '$start - $end';
  }

  String _calculateDuration() {
    if (widget.entry.duration != null) {
      return widget.entry.duration!;
    } else if (widget.entry.start != null && widget.entry.end != null) {
      // For active entries, calculate time since start
      if (widget.entry.isActive) {
        final startTime = _parseTimeString(widget.entry.date, widget.entry.start!);
        final now = DateTime.now();
        final duration = now.difference(startTime);
        final hours = duration.inHours;
        final minutes = duration.inMinutes % 60;
        return hours > 0 ? "${hours}h ${minutes}m" : "${minutes}m";
      }
      // For completed entries, calculate duration between start and end
      final minutes = calculateMinutesBetween(widget.entry.start!, widget.entry.end!);
      return minutesToTime(minutes);
    }
    return '';
  }

  DateTime _parseTimeString(String dateStr, String timeStr) {
    final date = DateTime.parse(dateStr);
    final timeParts = timeStr.split(':');
    return DateTime(
      date.year,
      date.month,
      date.day,
      int.parse(timeParts[0]),
      int.parse(timeParts[1]),
    );
  }

  @override
  Widget build(BuildContext context) {
    final Color baseColor = widget.projectColor ?? Theme.of(context).colorScheme.primary;
    final bool isDark = Theme.of(context).brightness == Brightness.dark;
    final bool isSmallScreen = ResponsiveUtils.isSmallScreen(context);

    return Slidable(
      endActionPane: ActionPane(
        motion: const ScrollMotion(),
        children: [
          SlidableAction(
            onPressed: (_) => widget.onEdit(widget.entry),
            backgroundColor: baseColor,
            foregroundColor: Colors.white,
            icon: Icons.edit,
            label: 'Edit',
            borderRadius: const BorderRadius.horizontal(left: Radius.circular(12)),
          ),
          SlidableAction(
            onPressed: (_) => widget.onDelete(widget.entry),
            backgroundColor: Colors.red.shade400,
            foregroundColor: Colors.white,
            icon: Icons.delete,
            label: 'Delete',
            borderRadius: const BorderRadius.horizontal(right: Radius.circular(12)),
          ),
        ],
      ),
      child: Material(
        elevation: 0.5,
        color: isDark
            ? Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3)
            : Theme.of(context).cardColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.0),
          side: BorderSide(
            color: Theme.of(context).dividerColor.withOpacity(isDark ? 0.2 : 0.1),
            width: 0.8,
          ),
        ),
        child: InkWell(
          onTap: () => widget.onEdit(widget.entry),
          borderRadius: BorderRadius.circular(12.0),
          child: Padding(
            padding: EdgeInsets.symmetric(
              vertical: 8,
              horizontal: isSmallScreen ? 10 : 12
            ),
            child: Row(
              children: [
                // Left section (Date and time info)
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      FutureBuilder<String>(
                        future: _formattedDateFuture,
                        builder: (context, snapshot) {
                          if (snapshot.connectionState == ConnectionState.waiting) {
                            return const SizedBox();
                          } else if (snapshot.hasError) {
                            return Text(
                              'Error loading date',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 14.5,
                                color: Theme.of(context).colorScheme.onSurface,
                              ),
                            );
                          } else {
                            return Text(
                              snapshot.data!,
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 14.5,
                                color: Theme.of(context).colorScheme.onSurface,
                              ),
                            );
                          }
                        },
                      ),
                      const SizedBox(height: 2),
                      widget.entry.isTimeRange
                        ? Row(
                            children: [
                              Icon(
                                widget.entry.isActive ? Icons.sync : Icons.schedule,
                                size: 12,
                                color: widget.entry.isActive
                                  ? Colors.green.withOpacity(0.9)
                                  : Theme.of(context).colorScheme.secondary.withOpacity(0.7),
                              ),
                              const SizedBox(width: 3),
                              Flexible(
                                child: Row(
                                  children: [
                                    Flexible(
                                      child: Text(
                                        _formatTimeRange(widget.entry.start!, widget.entry.end!),
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: widget.entry.isActive
                                            ? Colors.green
                                            : Theme.of(context).colorScheme.secondary,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                    if (widget.entry.isActive) ...[
                                      const SizedBox(width: 4),
                                      SizedBox(
                                        width: 8,
                                        height: 8,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          color: Colors.green,
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              ),
                            ],
                          )
                        : Row(
                            children: [
                              Icon(
                                Icons.hourglass_bottom,
                                size: 12,
                                color: Theme.of(context).colorScheme.secondary.withOpacity(0.7),
                              ),
                              const SizedBox(width: 3),
                              Flexible(
                                child: Text(
                                  'Manual duration',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Theme.of(context).colorScheme.secondary,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                    ],
                  ),
                ),
                // Right section (Duration and actions)
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 8.0,
                    vertical: 4.0
                  ),
                  decoration: BoxDecoration(
                    color: widget.entry.isActive
                      ? Colors.green.withOpacity(0.2)
                      : Theme.of(context).colorScheme.surfaceContainerHighest.withOpacity(isDark ? 0.5 : 1),
                    borderRadius: BorderRadius.circular(16.0),
                  ),
                  child: Text(
                    _currentDuration,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 12.5,
                      color: widget.entry.isActive
                        ? Colors.green
                        : Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
                // Show only one button on extremely small screens
                if (MediaQuery.of(context).size.width < 300)
                  IconButton(
                    icon: Icon(
                      Icons.more_vert,
                      size: 18.0,
                      color: Colors.blue.shade400.withOpacity(0.9),
                    ),
                    onPressed: () => widget.onEdit(widget.entry), // We'll use edit since it opens the entry details
                    tooltip: 'Edit entry',
                    splashRadius: 18.0,
                    constraints: const BoxConstraints(),
                    padding: const EdgeInsets.only(left: 6.0),
                    visualDensity: VisualDensity.compact,
                  )
                else ...[
                  Padding(
                    padding: EdgeInsets.only(left: 2.0),
                    child: Padding  (
                      padding: EdgeInsets.all(3.0),
                      child: InkWell(
                        onTap: () => widget.onEdit(widget.entry),
                        borderRadius: BorderRadius.circular(20.0),
                        child: Icon(
                          Icons.edit_outlined,
                          size: 18.0,
                          color: Colors.blue.shade400.withOpacity(0.9),
                        ),
                      ),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(left: 2.0),
                    child: Padding(
                      padding: EdgeInsets.all(3.0),
                      child: InkWell(
                        onTap: () => widget.onDelete(widget.entry),
                        borderRadius: BorderRadius.circular(20.0),
                        child: Icon(
                          Icons.delete_outline,
                          size: 18.0,
                          color: Colors.red.shade400.withOpacity(0.9),
                        ),
                      ),
                    ),
                  )
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
