import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:location/location.dart';
import 'package:time_tracker_flutter/models/location_area.dart';
import 'package:time_tracker_flutter/models/project.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/services/location_service.dart';
import 'package:time_tracker_flutter/services/location/location_events.dart';
import 'package:time_tracker_flutter/services/location/location_permissions.dart';
import 'package:time_tracker_flutter/widgets/responsive_dialog.dart';

class LocationAreaSelectorDialog extends StatefulWidget {
  final Project project;
  final LocationArea? existingArea;

  const LocationAreaSelectorDialog({
    Key? key,
    required this.project,
    this.existingArea,
  }) : super(key: key);

  @override
  _LocationAreaSelectorDialogState createState() => _LocationAreaSelectorDialogState();

  static Future<void> show(BuildContext context, Project project, {LocationArea? existingArea}) async {
    await showDialog<LocationArea>(
      context: context,
      builder: (context) => LocationAreaSelectorDialog(
        project: project,
        existingArea: existingArea,
      ),
    );
  }
}

class _LocationAreaSelectorDialogState extends State<LocationAreaSelectorDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _radiusController = TextEditingController(text: '100');
  final _cooldownController = TextEditingController(text: '60');

  late final MapController _mapController;
  LatLng _currentLocation = const LatLng(0, 0);
  double _radius = 100.0;
  bool _isLoading = true;

  final DatabaseService _databaseService = DatabaseService();
  final LocationService _locationService = LocationService();
  List<LocationArea> _areas = [];

  bool _isAddingArea = false;
  bool _isEditingArea = false;
  LocationArea? _selectedArea;

  // Event subscriptions for real-time updates
  StreamSubscription<LocationEvent>? _eventSubscription;
  StreamSubscription<TrackingStatus>? _statusSubscription;

  @override
  void initState() {
    super.initState();
    _mapController = MapController();

    // If editing existing area, populate fields
    if (widget.existingArea != null) {
      _selectedArea = widget.existingArea;
      _isEditingArea = true;
      _setupEditArea();
    }

    _loadAreas();
    _subscribeToLocationEvents();

    // Delay map operations until after build is complete
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_selectedArea != null) {
        try {
          _mapController.move(
            LatLng(_selectedArea!.centerLatitude, _selectedArea!.centerLongitude),
            15
          );
        } catch (e) {
          debugPrint('Error moving map: $e');
        }
      }
    });
  }

  void _setupEditArea() {
    _nameController.text = _selectedArea?.name ?? '';
    _radiusController.text = (_selectedArea?.radius.toString() ?? '100');
    _cooldownController.text = (_selectedArea?.cooldownTime.toString() ?? '60');

    if (_selectedArea != null) {
      _currentLocation = LatLng(
        _selectedArea!.centerLatitude,
        _selectedArea!.centerLongitude
      );
      _radius = _selectedArea!.radius;
    }
  }

  Future<void> _loadAreas() async {
    final areas = await _databaseService.getLocationAreas(projectId: widget.project.id);

    setState(() {
      _areas = areas;
      _isLoading = false;
    });

    // If we have areas but none selected, select the first one
    if (_areas.isNotEmpty && _selectedArea == null && !_isAddingArea) {
      _selectedArea = _areas.first;
      _setupEditArea();
    }
  }

  Future<void> _refreshAreas() async {
    final areas = await _databaseService.getLocationAreas(projectId: widget.project.id);

    setState(() {
      _areas = areas;
    });
  }

  @override
  void dispose() {
    _eventSubscription?.cancel();
    _statusSubscription?.cancel();
    _nameController.dispose();
    _radiusController.dispose();
    _cooldownController.dispose();
    super.dispose();
  }

  void _subscribeToLocationEvents() {
    // Listen for location events that might affect areas for this project
    _eventSubscription = _locationService.eventStream
        .where((event) => event.projectId == widget.project.id)
        .listen((event) {
      if (!mounted) return;

      // Only reload areas for critical events that change area status
      switch (event.type) {
        case LocationEventType.trackingStarted:
        case LocationEventType.trackingStopped:
          _loadAreas();
          break;
        default:
          break;
      }
    });

    // Listen for overall status changes - but don't reload constantly
    _statusSubscription = _locationService.statusStream.listen((status) {
      // Status changes will be reflected through event stream, no need to reload here
    });
  }

  Future<void> _getCurrentLocation() async {
    if (!mounted) return;

    final location = Location();

    bool serviceEnabled = await location.serviceEnabled();
    if (!serviceEnabled) {
      serviceEnabled = await location.requestService();
      if (!serviceEnabled) {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
        return;
      }
    }

    if (!mounted) return;

    PermissionStatus permissionStatus = await location.hasPermission();
    if (permissionStatus == PermissionStatus.denied) {
      permissionStatus = await location.requestPermission();
      if (permissionStatus != PermissionStatus.granted) {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
        return;
      }
    }

    if (!mounted) return;

    try {
      final locationData = await location.getLocation();

      if (!mounted) return;

      if (locationData.latitude != null && locationData.longitude != null) {
        // Only update current location if we're adding a new area or no area is selected
        if (_isAddingArea || _selectedArea == null) {
          setState(() {
            _currentLocation = LatLng(locationData.latitude!, locationData.longitude!);
            _isLoading = false;
          });
        } else {
          setState(() {
            _isLoading = false;
          });
        }

        // Wrap map operations in try-catch to handle initialization issues
        try {
          if (_selectedArea != null) {
            _mapController.move(LatLng(_selectedArea!.centerLatitude, _selectedArea!.centerLongitude), 15);
          } else {
            _mapController.move(_currentLocation, 15);
          }
        } catch (e) {
          debugPrint('Error moving map: $e');
        }
      } else {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      debugPrint('Error getting current location: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _onMapTap(TapPosition tapPosition, LatLng point) {
    if (_isAddingArea || _isEditingArea) {
      setState(() {
        _currentLocation = point;
      });
    }
  }

  void _toggleTracking(LocationArea area, bool isActive) async {
    try {
      if (isActive) {
        // Show immediate loading indicator
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                SizedBox(width: 12),
                Text('Starting location tracking...'),
              ],
            ),
            duration: Duration(seconds: 2),
          ),
        );

        final hasPermission = await LocationPermissions.requestLocationPermission();
        if (!hasPermission) {
          if (!mounted) return;
          ScaffoldMessenger.of(context).clearSnackBars();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Location permission is required for tracking')),
          );
          return;
        }

        // Start tracking with shorter timeout for better UX
        final startResult = await Future.any([
          _locationService.startTracking(area),
          Future.delayed(const Duration(seconds: 5), () => false),
        ]);

        if (!startResult) {
          if (!mounted) return;
          ScaffoldMessenger.of(context).clearSnackBars();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to start location tracking. Please try again.'),
              duration: Duration(seconds: 3),
            ),
          );
          return;
        }

        // Immediately update the local state to reflect the change
        setState(() {
          final areaIndex = _areas.indexWhere((a) => a.id == area.id);
          if (areaIndex != -1) {
            _areas[areaIndex] = _areas[areaIndex].copyWith(isActive: true);
          }
        });
      } else {
        // Stop tracking for this specific area
        await _locationService.stopTracking(area);

        // Immediately update the local state to reflect the change
        setState(() {
          final areaIndex = _areas.indexWhere((a) => a.id == area.id);
          if (areaIndex != -1) {
            _areas[areaIndex] = _areas[areaIndex].copyWith(isActive: false);
          }
        });
      }

      // Refresh the areas list to update toggle states
      await _refreshAreas();

      if (mounted) {
        // Show immediate confirmation to user
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isActive
              ? 'Tracking enabled for ${area.name}. Time tracking will start when you enter the area.'
              : 'Tracking disabled for ${area.name}'
            ),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error toggling tracking: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error ${isActive ? "starting" : "stopping"} tracking: ${e.toString()}'),
            duration: const Duration(seconds: 3),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _deleteArea(LocationArea area) async {
    // First check if it's being tracked
    if (area.isActive) {
      await _locationService.stopTracking();
    }

    // Then delete from database
    await _databaseService.deleteLocationArea(area.id);

    if (_selectedArea?.id == area.id) {
      _selectedArea = null;
      _isEditingArea = false;
      _isAddingArea = false;
    }

    _loadAreas();
  }

  void _addArea() {
    setState(() {
      _isAddingArea = true;
      _isEditingArea = false;
      _selectedArea = null;
      _nameController.text = '';
      _radiusController.text = '100';
      _cooldownController.text = '60';
      _radius = 100.0;
      _isLoading = true;
    });

    _getCurrentLocation();
  }

  void _editArea(LocationArea area) {
    setState(() {
      _selectedArea = area;
      _isEditingArea = true;
      _isAddingArea = false;
      _setupEditArea();
    });

    // Wrap map operations in try-catch to handle initialization issues
    try {
      _mapController.move(LatLng(area.centerLatitude, area.centerLongitude), 15);
    } catch (e) {
      debugPrint('Error moving map: $e');

      // Schedule a retry after the frame is built
      WidgetsBinding.instance.addPostFrameCallback((_) {
        try {
          _mapController.move(LatLng(area.centerLatitude, area.centerLongitude), 15);
        } catch (e) {
          debugPrint('Error moving map after rebuild: $e');
        }
      });
    }
  }

  void _saveArea() async {
    if (!_formKey.currentState!.validate()) return;

    // Parse radius and cooldown values
    final radius = double.tryParse(_radiusController.text) ?? 100.0;
    final cooldown = int.tryParse(_cooldownController.text) ?? 60;

    // Create or update location area
    final locationArea = _isEditingArea && _selectedArea != null
        ? _selectedArea!.copyWith(
            name: _nameController.text,
            centerLatitude: _currentLocation.latitude,
            centerLongitude: _currentLocation.longitude,
            radius: radius,
            cooldownTime: cooldown,
          )
        : LocationArea(
            projectId: widget.project.id,
            name: _nameController.text,
            centerLatitude: _currentLocation.latitude,
            centerLongitude: _currentLocation.longitude,
            radius: radius,
            cooldownTime: cooldown,
          );

    // Save to database and update tracking service if needed
    if (_isEditingArea && _selectedArea != null) {
      // For existing areas, use LocationService to handle updates and notify background service
      await _locationService.updateLocationArea(locationArea);
    } else {
      // For new areas, save directly to database
      await _databaseService.saveLocationArea(locationArea);
    }

    setState(() {
      _isAddingArea = false;
      _isEditingArea = false;
      _selectedArea = null;
    });

    _loadAreas();
  }

  void _cancelEditing() {
    setState(() {
      _isAddingArea = false;
      _isEditingArea = false;
    });

    if (_selectedArea != null) {
      _setupEditArea();
    }
  }

  Widget _buildAreaList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        const Text(
          'Location Areas',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        _areas.isEmpty
            ? const Text('No location areas defined')
            : ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _areas.length,
                itemBuilder: (context, index) {
                  final area = _areas[index];
                  final isTracking = area.isActive;
                  final isSelected = _selectedArea?.id == area.id;

                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    color: isSelected ? Theme.of(context).primaryColor.withOpacity(0.1) : null,
                    child: ListTile(
                      title: Text(area.name),
                      subtitle: Text('Radius: ${area.radius.toInt()}m, Cooldown: ${area.cooldownTime}s'),
                      leading: Icon(Icons.location_on,
                        color: isTracking ? Colors.green : Colors.grey,
                      ),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Switch(
                            value: isTracking,
                            onChanged: (value) => _toggleTracking(area, value),
                          ),
                          IconButton(
                            icon: const Icon(Icons.edit),
                            onPressed: () => _editArea(area),
                          ),
                          IconButton(
                            icon: const Icon(Icons.delete),
                            onPressed: () => _deleteArea(area),
                          ),
                        ],
                      ),
                      onTap: () {
                        if (!_isAddingArea && !_isEditingArea) {
                          _editArea(area);
                        }
                      },
                    ),
                  );
                },
              ),
        const SizedBox(height: 16),
        if (!_isAddingArea && !_isEditingArea)
          ElevatedButton(
            onPressed: _addArea,
            child: const Text('Add Location Area'),
          ),
      ],
    );
  }

  Widget _buildAreaForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            _isEditingArea ? 'Edit Location Area' : 'Add Location Area',
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _nameController,
            decoration: const InputDecoration(
              labelText: 'Location Name',
              border: OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter a name';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _radiusController,
                  decoration: const InputDecoration(
                    labelText: 'Radius (meters)',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Required';
                    }
                    if (double.tryParse(value) == null) {
                      return 'Invalid number';
                    }
                    return null;
                  },
                  onChanged: (value) {
                    setState(() {
                      _radius = double.tryParse(value) ?? 100.0;
                    });
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: _cooldownController,
                  decoration: const InputDecoration(
                    labelText: 'Cooldown (seconds)',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Required';
                    }
                    if (int.tryParse(value) == null) {
                      return 'Invalid number';
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: _cancelEditing,
                  child: const Text('Cancel'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: _saveArea,
                  child: const Text('Save'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMap() {
    return Container(
      height: 300,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: FlutterMap(
          mapController: _mapController,
          options: MapOptions(
            initialCenter: _currentLocation,
            initialZoom: 15.0,
            onTap: _onMapTap,
            backgroundColor: Colors.white, // Explicit background color
          ),
          children: [
            TileLayer(
              urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
              userAgentPackageName: 'com.example.time_tracker_flutter',
              maxZoom: 19,
              minZoom: 1,
              tileProvider: NetworkTileProvider(),
              tileBuilder: (context, tileWidget, tile) {
                return tileWidget;
              },
            ),
            CircleLayer(
              circles: [
                CircleMarker(
                  point: _currentLocation,
                  radius: _radius,
                  color: Colors.blue.withOpacity(0.3),
                  borderColor: Colors.blue,
                  borderStrokeWidth: 2.0,
                ),
              ],
            ),
            MarkerLayer(
              markers: [
                Marker(
                  point: _currentLocation,
                  width: 40,
                  height: 40,
                  child: const Icon(
                    Icons.location_on,
                    color: Colors.red,
                    size: 40,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveDialog(
      title: Text('Location Areas - ${widget.project.name}'),
      content: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  if (_isAddingArea || _isEditingArea) ...[
                    _buildMap(),
                    const SizedBox(height: 16),
                    _buildAreaForm(),
                  ] else ...[
                    _buildAreaList(),
                  ],
                ],
              ),
            ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
      scrollable: true,
    );
  }
}